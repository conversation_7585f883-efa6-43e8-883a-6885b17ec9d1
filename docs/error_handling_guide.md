# Error Handling and Reporting Guide

This guide explains the comprehensive error handling and reporting system implemented in the Darve mobile app.

## Overview

The error handling system provides:
- **Structured Error Models** - Type-safe error handling with severity levels
- **Automatic Error Parsing** - Parse API error responses into user-friendly messages
- **Error Reporting** - Automatic reporting to <PERSON>try for monitoring and debugging
- **User-Friendly Display** - Enhanced error messages with appropriate styling
- **Breadcrumb Tracking** - Track user actions leading to errors

## Architecture

### Error Models (`lib/services/error/error_models.dart`)

#### Base Classes
- `AppError` - Base error class with severity levels
- `ErrorSeverity` - Enum for error classification (expected, warning, error, fatal)

#### Specific Error Types
- `ValidationError` - API validation errors with field-specific messages
- `NetworkError` - HTTP and network related errors
- `AuthError` - Authentication and authorization errors
- `AppLogicError` - Application logic errors
- `UserError` - User-facing errors

### Error Parser (`lib/services/error/error_parser.dart`)

Automatically parses API error responses like:
```json
{
  "error": "username: Letters, numbers and '_'. Minimum 6 characters\npassword: Min 6 characters",
  "req_id": "6185ec98-d7d9-43d8-b432-d30e7c9bd9de"
}
```

Into structured `ValidationError` objects with field-specific messages.

### Error Reporting (`lib/services/error/error_reporting_service.dart`)

- **Sentry Integration** - Automatic error reporting to Sentry
- **Smart Filtering** - Only reports unexpected errors (not validation errors)
- **Context Tracking** - Adds user context and breadcrumbs
- **Privacy Protection** - Filters out sensitive information

### Error Interceptor (`lib/services/http/interceptors/error_interceptor.dart`)

- **Automatic Processing** - Intercepts all HTTP errors
- **Error Parsing** - Converts DioExceptions to AppError objects
- **Breadcrumb Tracking** - Adds HTTP request/response breadcrumbs
- **Detailed Logging** - Enhanced error logging in debug mode

## Setup

### 1. Environment Variables

Add to your `.env` file:
```env
SENTRY_DSN=your_sentry_dsn_here
ENVIRONMENT=development  # or production
```

### 2. Dependencies

The system uses:
- `sentry_flutter: ^8.9.0` - Error reporting
- `dio: ^5.8.0+1` - HTTP client with interceptors

### 3. Initialization

Error reporting is automatically initialized in `main.dart`:
```dart
void main() async {
  // ... other initialization
  
  // Initialize error reporting
  final sentryDsn = dotenv.env['SENTRY_DSN'];
  if (sentryDsn != null && sentryDsn.isNotEmpty) {
    await ErrorReportingService.initialize(
      dsn: sentryDsn,
      environment: AppConfig.instance.environment,
      sampleRate: AppConfig.instance.isProduction ? 0.1 : 1.0,
    );
  }
  
  runApp(MyApp());
}
```

## Usage

### Displaying Errors to Users

```dart
import 'package:darve/utils/errors.dart';

final errorHandler = ErrorsHandle();

// Display any error (automatically handles AppError objects)
errorHandler.displayErrorToast(error);

// Display success message
errorHandler.displaySuccessToast('Operation completed!');

// Display info message
errorHandler.displayInfoToast('Information message');
```

### Handling API Errors

The system automatically handles API errors through the HTTP interceptor:

```dart
try {
  final response = await dioService.post('/api/login', data: {
    'username': username,
    'password': password,
  });
  // Handle success
} catch (e) {
  // Error is automatically parsed and can be displayed
  if (e is ValidationError) {
    // Show field-specific validation errors
    errorHandler.displayErrorToast(e);
  } else if (e is NetworkError) {
    // Show network error
    errorHandler.displayErrorToast(e);
  } else {
    // Handle other errors
    errorHandler.displayErrorToast(e);
  }
}
```

### Manual Error Reporting

```dart
import 'package:darve/services/error/error_reporting_service.dart';

final errorReporting = ErrorReportingService.instance;

// Report an error
await errorReporting.reportError(appError, stackTrace: stackTrace);

// Report an exception
await errorReporting.reportException(exception, context: 'user_action');

// Add breadcrumb for tracking
errorReporting.addBreadcrumb(
  message: 'User clicked login button',
  category: 'user_action',
  data: {'username': username},
);

// Set user context
errorReporting.setTag('user_id', userId);
errorReporting.setContext('user', {'username': username});
```

### Creating Custom Errors

```dart
// Validation error
final validationError = ValidationError(
  message: 'Validation failed',
  fieldErrors: {
    'username': ['Must be at least 6 characters'],
    'password': ['Required field'],
  },
  requestId: 'req_123',
);

// Network error
final networkError = NetworkError.serverError(
  statusCode: 500,
  message: 'Server error',
  endpoint: '/api/endpoint',
  method: 'POST',
);

// User error
final userError = UserError.custom('Custom error message');
```

## Error Severity Levels

- **Expected** - Validation errors, user input errors (not reported to Sentry)
- **Warning** - Network timeouts, session expired (reported with low priority)
- **Error** - Server errors, unexpected API responses (reported normally)
- **Fatal** - App crashes, critical failures (reported with high priority)

## Best Practices

1. **Use Specific Error Types** - Use ValidationError for form validation, NetworkError for HTTP issues, etc.
2. **Add Context** - Include request IDs, user actions, and relevant metadata
3. **Don't Over-Report** - Expected errors (validation) are not reported to Sentry
4. **Add Breadcrumbs** - Track user actions leading to errors
5. **Handle Gracefully** - Always provide user-friendly error messages

## Error Response Formats Supported

### Validation Errors
```json
{
  "error": "field1: Error message 1\nfield2: Error message 2",
  "req_id": "request_id"
}
```

### Simple Errors
```json
{
  "error": "Error message",
  "req_id": "request_id"
}
```

### Standard HTTP Errors
- 401 Unauthorized
- 403 Forbidden
- 404 Not Found
- 429 Rate Limited
- 500+ Server Errors

## Monitoring and Debugging

- **Sentry Dashboard** - View error reports, trends, and user impact
- **Debug Logs** - Detailed error information in debug mode
- **Breadcrumbs** - Track user actions leading to errors
- **Request IDs** - Correlate frontend errors with backend logs

## Testing

Use the example widget to test error handling:
```dart
import 'package:darve/services/error/error_example.dart';

// Navigate to ErrorHandlingExampleWidget to test different error types
```

This comprehensive error handling system ensures that:
- Users see helpful, actionable error messages
- Developers get detailed error reports for debugging
- The app gracefully handles all types of errors
- Error trends and patterns can be monitored and analyzed
