# Repository Error Handling Guide

## Overview

This guide explains the proper separation of concerns for error handling in repository classes. Repository classes should **only propagate errors** to the UI layer, not handle or display them directly.

## ✅ Correct Pattern: Error Propagation

### Repository Layer (Data Access)
Repository classes should focus on data access and let errors bubble up naturally:

```dart
class AuthRepository {
  final HttpService _dioService;

  AuthRepository(this._dioService);

  // ✅ CORRECT: Let errors propagate naturally
  Future<AuthResponseModel> login(String username, String password) async {
    final response = await _dioService.post(
      '/api/login',
      data: {
        'username': username,
        'password': password,
      },
    );

    return AuthResponseModel.fromJson(response.data);
  }

  // ✅ CORRECT: No error handling in repository
  Future<UserModel> getUserProfile(String userId) async {
    final response = await _dioService.get('/api/users/$userId');
    return UserModel.fromMap(response.data);
  }
}
```

### Service Layer (Business Logic)
Services can catch and transform errors if needed:

```dart
class AuthService {
  final AuthRepository _authRepository;

  AuthService(this._authRepository);

  Future<void> signInWithEmailPassword(String username, String password) async {
    try {
      final authResponse = await _authRepository.login(username, password);
      // Handle successful login
      await _handleSuccessfulAuth(authResponse.token, authResponse.user);
    } catch (error) {
      // Transform or log error, then re-throw or handle appropriately
      _handleAuthError(error);
    }
  }
}
```

### UI Layer (Presentation)
UI components handle error display:

```dart
class LoginController extends GetxController {
  final AuthService _authService;
  final ErrorsHandle _errorHandler = ErrorsHandle();

  Future<void> login() async {
    try {
      await _authService.signInWithEmailPassword(username.text, password.text);
      // Navigate to home or show success
    } catch (error) {
      // ✅ CORRECT: Handle error display in UI layer
      _errorHandler.displayErrorToast(error);
    }
  }
}
```

## ❌ Incorrect Pattern: Direct Error Handling in Repository

### What NOT to do:

```dart
class BadRepository {
  final HttpService _dioService;
  final ErrorsHandle _errorHandler = ErrorsHandle(); // ❌ WRONG

  // ❌ WRONG: Repository should not handle UI concerns
  Future<UserModel?> getUserProfile(String userId) async {
    try {
      final response = await _dioService.get('/api/users/$userId');
      return UserModel.fromMap(response.data);
    } catch (error) {
      // ❌ WRONG: Repository showing errors directly
      _errorHandler.displayErrorToast(error);
      return null; // ❌ WRONG: Hiding errors from caller
    }
  }
}
```

## 🎯 Benefits of Proper Error Propagation

### 1. **Separation of Concerns**
- Repository: Data access only
- Service: Business logic and error transformation
- UI: Error display and user interaction

### 2. **Testability**
- Easy to test repository methods without UI dependencies
- Can mock error scenarios in unit tests
- Clear error boundaries

### 3. **Flexibility**
- Different UI components can handle the same error differently
- Services can transform errors based on business logic
- Centralized error handling policies

### 4. **Maintainability**
- Single responsibility principle
- Easy to change error handling without affecting data access
- Clear error flow through the application

## 🔧 Error Handling Flow

```
API Error Response
       ↓
HTTP Service (with ErrorInterceptor)
       ↓
Repository (propagates error)
       ↓
Service (optional transformation)
       ↓
UI Controller (displays error)
       ↓
User sees friendly error message
```

## 📝 Implementation Examples

### Example 1: Simple Repository Method
```dart
class PostsRepository {
  final HttpService _dioService;

  PostsRepository(this._dioService);

  // ✅ Clean, focused on data access
  Future<List<Post>> getPosts(String username) async {
    final response = await _dioService.get('/api/posts/$username');
    return (response.data as List)
        .map((json) => Post.fromJson(json))
        .toList();
  }
}
```

### Example 2: Repository with Data Transformation
```dart
class ProfileRepository {
  final HttpService _dioService;

  ProfileRepository(this._dioService);

  // ✅ Handles data transformation, lets errors propagate
  Future<ProfileData> getProfileData(String username) async {
    if (username.startsWith("@")) {
      username = username.split("@")[1];
    }

    final response = await _dioService.get('/u/$username');
    
    if (response.data != null) {
      return ProfileData.fromJson(response.data);
    }
    
    return ProfileData.empty();
  }
}
```

### Example 3: UI Controller Handling Repository Errors
```dart
class ProfileController extends GetxController {
  final ProfileRepository _profileRepository;
  final ErrorsHandle _errorHandler = ErrorsHandle();

  var isLoading = false.obs;
  var profileData = Rx<ProfileData?>(null);

  Future<void> loadProfile(String username) async {
    try {
      isLoading.value = true;
      profileData.value = await _profileRepository.getProfileData(username);
    } catch (error) {
      // ✅ UI layer handles error display
      if (error is ApiError) {
        _errorHandler.displayErrorToast(error);
      } else {
        _errorHandler.displayErrorToast(
          UserError.custom('Failed to load profile')
        );
      }
    } finally {
      isLoading.value = false;
    }
  }
}
```

## 🚀 Best Practices

### 1. **Keep Repositories Pure**
- Focus only on data access
- No UI dependencies
- No error display logic

### 2. **Use Proper Error Types**
- Let `ApiError` handle server responses
- Use `NetworkError` for connectivity issues
- Use `AppLogicError` for application logic problems

### 3. **Handle Errors at the Right Level**
- Repository: Data access errors
- Service: Business logic errors
- UI: User-facing error display

### 4. **Provide Context**
- Include request IDs for debugging
- Add relevant metadata to errors
- Use meaningful error codes

### 5. **Test Error Scenarios**
- Unit test repository methods with mock errors
- Test service error transformation
- Test UI error display

## 📊 Error Handling Checklist

- [ ] Repository methods don't catch errors unnecessarily
- [ ] Repository methods don't display errors to users
- [ ] Service layer handles business logic errors appropriately
- [ ] UI controllers display errors to users
- [ ] Error types are appropriate for the context
- [ ] Error messages are user-friendly
- [ ] Request IDs are preserved for debugging
- [ ] Error scenarios are tested

This pattern ensures clean separation of concerns and makes your application more maintainable, testable, and flexible.
