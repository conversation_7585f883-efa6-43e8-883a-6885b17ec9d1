// Test file to demonstrate error parsing functionality
import 'package:flutter_test/flutter_test.dart';
import 'package:dio/dio.dart';
import 'package:darve/services/error/error_parser.dart';
import 'package:darve/services/error/error_models.dart';

void main() {
  group('Error Parsing Tests', () {
    test('Parse validation error from API response', () {
      // Simulate the API response you provided
      final responseData = {
        "error": "username: Letters, numbers and '_'. Minimum 6 characters\npassword: Min 6 characters",
        "req_id": "6185ec98-d7d9-43d8-b432-d30e7c9bd9de"
      };

      // Create a mock DioException
      final dioError = DioException(
        requestOptions: RequestOptions(
          path: '/api/login',
          method: 'POST',
        ),
        response: Response(
          requestOptions: RequestOptions(path: '/api/login'),
          statusCode: 400,
          data: responseData,
        ),
        type: DioExceptionType.badResponse,
      );

      // Parse the error
      final parsedError = ErrorParser.parseFromDioException(dioError);

      // Verify it's a ValidationError
      expect(parsedError, isA<ValidationError>());
      
      final validationError = parsedError as ValidationError;
      
      // Check field errors
      expect(validationError.fieldErrors['username'], 
             contains("Letters, numbers and '_'. Minimum 6 characters"));
      expect(validationError.fieldErrors['password'], 
             contains("Min 6 characters"));
      
      // Check request ID
      expect(validationError.requestId, equals("6185ec98-d7d9-43d8-b432-d30e7c9bd9de"));
      
      // Check formatted message
      expect(validationError.formattedMessage, 
             contains("username: Letters, numbers and '_'. Minimum 6 characters"));
      expect(validationError.formattedMessage, 
             contains("password: Min 6 characters"));
      
      // Check severity
      expect(validationError.severity, equals(ErrorSeverity.expected));
    });

    test('Parse simple error message', () {
      final responseData = {
        "error": "Invalid credentials",
        "req_id": "abc123"
      };

      final dioError = DioException(
        requestOptions: RequestOptions(path: '/api/login'),
        response: Response(
          requestOptions: RequestOptions(path: '/api/login'),
          statusCode: 401,
          data: responseData,
        ),
        type: DioExceptionType.badResponse,
      );

      final parsedError = ErrorParser.parseFromDioException(dioError);

      expect(parsedError, isA<AuthError>());
      expect(parsedError.message, contains("Unauthorized"));
      expect(parsedError.requestId, equals("abc123"));
    });

    test('Parse network timeout error', () {
      final dioError = DioException(
        requestOptions: RequestOptions(path: '/api/test'),
        type: DioExceptionType.connectionTimeout,
      );

      final parsedError = ErrorParser.parseFromDioException(dioError);

      expect(parsedError, isA<NetworkError>());
      expect(parsedError.message, contains("timeout"));
      expect(parsedError.severity, equals(ErrorSeverity.warning));
    });

    test('Parse server error', () {
      final dioError = DioException(
        requestOptions: RequestOptions(path: '/api/test'),
        response: Response(
          requestOptions: RequestOptions(path: '/api/test'),
          statusCode: 500,
          data: "Internal Server Error",
        ),
        type: DioExceptionType.badResponse,
      );

      final parsedError = ErrorParser.parseFromDioException(dioError);

      expect(parsedError, isA<NetworkError>());
      expect(parsedError.severity, equals(ErrorSeverity.error));
      
      final networkError = parsedError as NetworkError;
      expect(networkError.statusCode, equals(500));
    });

    test('ValidationError field access methods', () {
      final validationError = ValidationError(
        message: 'Validation failed',
        fieldErrors: {
          'username': ['Too short', 'Invalid characters'],
          'email': ['Invalid format'],
        },
      );

      // Test getFieldError method
      expect(validationError.getFieldError('username'), equals('Too short'));
      expect(validationError.getFieldError('email'), equals('Invalid format'));
      expect(validationError.getFieldError('nonexistent'), isNull);

      // Test formattedMessage
      final formatted = validationError.formattedMessage;
      expect(formatted, contains('username: Too short, Invalid characters'));
      expect(formatted, contains('email: Invalid format'));
    });

    test('Error severity mapping', () {
      final expectedError = ValidationError(
        message: 'Validation failed',
        fieldErrors: {'field': ['error']},
      );
      expect(expectedError.severity, equals(ErrorSeverity.expected));

      final warningError = NetworkError.timeout();
      expect(warningError.severity, equals(ErrorSeverity.warning));

      final fatalError = AppLogicError.unexpected('crash');
      expect(fatalError.severity, equals(ErrorSeverity.fatal));
    });
  });
}
