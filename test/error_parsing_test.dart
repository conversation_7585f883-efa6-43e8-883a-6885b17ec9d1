// Test file to demonstrate error parsing functionality
import 'package:flutter_test/flutter_test.dart';
import 'package:dio/dio.dart';
import 'package:darve/services/error/error_parser.dart';
import 'package:darve/services/error/error_models.dart';

void main() {
  group('Error Parsing Tests', () {
    test('Parse validation error from API response', () {
      // Simulate the API response you provided
      final responseData = {
        "error": "username: Letters, numbers and '_'. Minimum 6 characters\npassword: Min 6 characters",
        "req_id": "6185ec98-d7d9-43d8-b432-d30e7c9bd9de"
      };

      // Create a mock DioException
      final dioError = DioException(
        requestOptions: RequestOptions(
          path: '/api/login',
          method: 'POST',
        ),
        response: Response(
          requestOptions: RequestOptions(path: '/api/login'),
          statusCode: 400,
          data: responseData,
        ),
        type: DioExceptionType.badResponse,
      );

      // Parse the error
      final parsedError = ErrorParser.parseFromDioException(dioError);

      // Verify it's an ApiError
      expect(parsedError, isA<ApiError>());

      final apiError = parsedError as ApiError;

      // Check field errors
      expect(apiError.fieldErrors['username'],
             contains("Letters, numbers and '_'. Minimum 6 characters"));
      expect(apiError.fieldErrors['password'],
             contains("Min 6 characters"));

      // Check request ID
      expect(apiError.requestId, equals("6185ec98-d7d9-43d8-b432-d30e7c9bd9de"));

      // Check that it has field errors
      expect(apiError.hasFieldErrors, isTrue);

      // Check formatted message
      expect(apiError.formattedMessage,
             contains("username: Letters, numbers and '_'. Minimum 6 characters"));
      expect(apiError.formattedMessage,
             contains("password: Min 6 characters"));

      // Check display message
      expect(apiError.displayMessage, equals(apiError.formattedMessage));

      // Check severity
      expect(apiError.severity, equals(ErrorSeverity.expected));
    });

    test('Parse simple error message', () {
      final responseData = {
        "error": "Invalid credentials",
        "req_id": "abc123"
      };

      final dioError = DioException(
        requestOptions: RequestOptions(path: '/api/login'),
        response: Response(
          requestOptions: RequestOptions(path: '/api/login'),
          statusCode: 401,
          data: responseData,
        ),
        type: DioExceptionType.badResponse,
      );

      final parsedError = ErrorParser.parseFromDioException(dioError);

      expect(parsedError, isA<AuthError>());
      expect(parsedError.message, contains("Unauthorized"));
      expect(parsedError.requestId, equals("abc123"));
    });

    test('Parse network timeout error', () {
      final dioError = DioException(
        requestOptions: RequestOptions(path: '/api/test'),
        type: DioExceptionType.connectionTimeout,
      );

      final parsedError = ErrorParser.parseFromDioException(dioError);

      expect(parsedError, isA<NetworkError>());
      expect(parsedError.message, contains("timeout"));
      expect(parsedError.severity, equals(ErrorSeverity.warning));
    });

    test('Parse server error', () {
      final dioError = DioException(
        requestOptions: RequestOptions(path: '/api/test'),
        response: Response(
          requestOptions: RequestOptions(path: '/api/test'),
          statusCode: 500,
          data: "Internal Server Error",
        ),
        type: DioExceptionType.badResponse,
      );

      final parsedError = ErrorParser.parseFromDioException(dioError);

      expect(parsedError, isA<NetworkError>());
      expect(parsedError.severity, equals(ErrorSeverity.error));
      
      final networkError = parsedError as NetworkError;
      expect(networkError.statusCode, equals(500));
    });

    test('ApiError field access methods', () {
      final apiError = ApiError.withFields(
        message: 'API request failed',
        fieldErrors: {
          'username': ['Too short', 'Invalid characters'],
          'email': ['Invalid format'],
        },
      );

      // Test getFieldError method
      expect(apiError.getFieldError('username'), equals('Too short'));
      expect(apiError.getFieldError('email'), equals('Invalid format'));
      expect(apiError.getFieldError('nonexistent'), isNull);

      // Test hasFieldErrors
      expect(apiError.hasFieldErrors, isTrue);

      // Test formattedMessage
      final formatted = apiError.formattedMessage;
      expect(formatted, contains('username: Too short, Invalid characters'));
      expect(formatted, contains('email: Invalid format'));

      // Test displayMessage for field errors
      expect(apiError.displayMessage, equals(formatted));
    });

    test('ApiError simple error methods', () {
      final apiError = ApiError.simple(
        message: 'Simple error message',
        code: 'SIMPLE_ERROR',
      );

      // Test hasFieldErrors
      expect(apiError.hasFieldErrors, isFalse);

      // Test displayMessage for simple errors
      expect(apiError.displayMessage, equals('Simple error message'));
      expect(apiError.rawErrorMessage, equals('Simple error message'));
    });

    test('Error severity mapping', () {
      final expectedApiError = ApiError.withFields(
        message: 'API request failed',
        fieldErrors: {'field': ['error']},
      );
      expect(expectedApiError.severity, equals(ErrorSeverity.expected));

      final simpleApiError = ApiError.simple(
        message: 'Simple API error',
        severity: ErrorSeverity.expected,
      );
      expect(simpleApiError.severity, equals(ErrorSeverity.expected));

      final warningError = NetworkError.timeout();
      expect(warningError.severity, equals(ErrorSeverity.warning));

      final fatalError = AppLogicError.unexpected('crash');
      expect(fatalError.severity, equals(ErrorSeverity.fatal));
    });
  });
}
