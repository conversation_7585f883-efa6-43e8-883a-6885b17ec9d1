// Enhanced error interceptor for parsing and reporting HTTP errors
import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import '../../../services/error/error_models.dart';
import '../../../services/error/error_parser.dart';
import '../../../services/error/error_reporting_service.dart';

/// Interceptor that handles HTTP errors, parses them into AppError objects,
/// and reports them to error reporting service
class ErrorInterceptor extends Interceptor {
  final ErrorReportingService _errorReporting = ErrorReportingService.instance;

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    // Add breadcrumb for request tracking
    _errorReporting.addBreadcrumb(
      message: 'HTTP Request: ${options.method} ${options.uri}',
      category: 'http.request',
      data: {
        'method': options.method,
        'url': options.uri.toString(),
        'headers': _sanitizeHeaders(options.headers),
      },
    );

    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    // Add breadcrumb for successful response
    _errorReporting.addBreadcrumb(
      message: 'HTTP Response: ${response.statusCode} ${response.requestOptions.uri}',
      category: 'http.response',
      data: {
        'status_code': response.statusCode,
        'method': response.requestOptions.method,
        'url': response.requestOptions.uri.toString(),
      },
    );

    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    // Parse the error into our structured format
    final appError = ErrorParser.parseFromDioException(err);

    // Log error details in debug mode
    if (kDebugMode) {
      _logErrorDetails(err, appError);
    }

    // Report error to error reporting service
    _reportError(appError, err);

    // Add breadcrumb for error tracking
    _addErrorBreadcrumb(err, appError);

    // Create a new DioException with our parsed error
    final enhancedError = DioException(
      requestOptions: err.requestOptions,
      response: err.response,
      type: err.type,
      error: appError, // Replace original error with our parsed AppError
      message: appError.message,
      stackTrace: err.stackTrace,
    );

    handler.next(enhancedError);
  }

  /// Log detailed error information in debug mode
  void _logErrorDetails(DioException dioError, AppError appError) {
    final uri = dioError.requestOptions.uri.toString();
    final method = dioError.requestOptions.method.toUpperCase();
    
    debugPrint('\n' + '=' * 80);
    debugPrint('❌ HTTP ERROR INTERCEPTED');
    debugPrint('=' * 80);
    debugPrint('📍 URL: $method $uri');
    debugPrint('⏰ Time: ${DateTime.now().toIso8601String()}');
    debugPrint('🏷️ Error Type: ${appError.runtimeType}');
    debugPrint('🚨 Severity: ${appError.severity.name}');
    debugPrint('🔢 Code: ${appError.code ?? 'N/A'}');
    debugPrint('💬 Message: ${appError.message}');
    
    if (appError.requestId != null) {
      debugPrint('🆔 Request ID: ${appError.requestId}');
    }
    
    if (dioError.response != null) {
      final response = dioError.response!;
      debugPrint('📊 Status Code: ${response.statusCode}');
      debugPrint('📊 Status Message: ${response.statusMessage}');
      
      if (response.data != null) {
        debugPrint('📥 Error Response:');
        try {
          final prettyData = _formatJson(response.data);
          if (prettyData.length > 1000) {
            debugPrint('${prettyData.substring(0, 1000)}...\n[TRUNCATED - Response too large]');
          } else {
            debugPrint(prettyData);
          }
        } catch (e) {
          final dataStr = response.data.toString();
          if (dataStr.length > 1000) {
            debugPrint('${dataStr.substring(0, 1000)}...\n[TRUNCATED - Response too large]');
          } else {
            debugPrint(dataStr);
          }
        }
      }
    }

    // Log validation errors in a user-friendly format
    if (appError is ValidationError) {
      debugPrint('📝 Validation Errors:');
      appError.fieldErrors.forEach((field, errors) {
        debugPrint('   $field: ${errors.join(', ')}');
      });
    }

    // Log network error details
    if (appError is NetworkError) {
      debugPrint('🌐 Network Error Details:');
      debugPrint('   Status Code: ${appError.statusCode}');
      debugPrint('   Endpoint: ${appError.endpoint}');
      debugPrint('   Method: ${appError.method}');
    }
    
    debugPrint('=' * 80);
  }

  /// Report error to error reporting service
  void _reportError(AppError appError, DioException dioError) {
    try {
      // Report to error reporting service
      _errorReporting.reportError(appError, stackTrace: dioError.stackTrace);
    } catch (e) {
      debugPrint('❌ Failed to report error: $e');
    }
  }

  /// Add breadcrumb for error tracking
  void _addErrorBreadcrumb(DioException dioError, AppError appError) {
    try {
      _errorReporting.addBreadcrumb(
        message: 'HTTP Error: ${appError.code ?? appError.runtimeType} - ${appError.message}',
        category: 'http.error',
        level: _mapSeverityToSentryLevel(appError.severity),
        data: {
          'error_type': appError.runtimeType.toString(),
          'error_code': appError.code,
          'severity': appError.severity.name,
          'status_code': dioError.response?.statusCode,
          'method': dioError.requestOptions.method,
          'url': dioError.requestOptions.uri.toString(),
          'request_id': appError.requestId,
        },
      );
    } catch (e) {
      debugPrint('❌ Failed to add error breadcrumb: $e');
    }
  }

  /// Map AppError severity to Sentry level
  dynamic _mapSeverityToSentryLevel(ErrorSeverity severity) {
    // Use SentryLevel enum from sentry_flutter
    switch (severity) {
      case ErrorSeverity.expected:
        return SentryLevel.info;
      case ErrorSeverity.warning:
        return SentryLevel.warning;
      case ErrorSeverity.error:
        return SentryLevel.error;
      case ErrorSeverity.fatal:
        return SentryLevel.fatal;
    }
  }

  /// Format JSON data for pretty printing
  String _formatJson(dynamic data) {
    try {
      if (data is String) {
        // Try to parse and re-format if it's a JSON string
        final decoded = jsonDecode(data);
        return jsonEncode(decoded);
      } else {
        return jsonEncode(data);
      }
    } catch (e) {
      return data.toString();
    }
  }

  /// Sanitize headers to remove sensitive information
  Map<String, dynamic> _sanitizeHeaders(Map<String, dynamic> headers) {
    final sanitized = <String, dynamic>{};
    
    headers.forEach((key, value) {
      final lowerKey = key.toLowerCase();
      if (_isSensitiveHeader(lowerKey)) {
        sanitized[key] = '[REDACTED]';
      } else {
        sanitized[key] = value;
      }
    });
    
    return sanitized;
  }

  /// Check if a header contains sensitive information
  bool _isSensitiveHeader(String key) {
    return key.contains('cookie') || 
           key.contains('authorization') ||
           key.contains('token') ||
           key.contains('secret') ||
           key.contains('password');
  }
}


