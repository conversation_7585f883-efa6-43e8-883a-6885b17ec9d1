// Error reporting service with Sentry integration
import 'package:flutter/foundation.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'error_models.dart';

/// Service for reporting errors to external services (Sentry)
class ErrorReportingService {
  static ErrorReportingService? _instance;
  static ErrorReportingService get instance => _instance ??= ErrorReportingService._();
  
  ErrorReportingService._();

  bool _isInitialized = false;

  /// Initialize Sentry for error reporting
  /// Call this in main() before runApp()
  static Future<void> initialize({
    required String dsn,
    String? environment,
    double? sampleRate,
  }) async {
    try {
      await SentryFlutter.init(
        (options) {
          options.dsn = dsn;
          options.environment = environment ?? (kDebugMode ? 'development' : 'production');
          options.sampleRate = sampleRate ?? (kDebugMode ? 1.0 : 0.1);
          options.tracesSampleRate = kDebugMode ? 1.0 : 0.1;
          
          // Configure what to capture
          options.captureFailedRequests = true;
          options.sendDefaultPii = false; // Don't send personally identifiable information
          
          // Set release and dist for better tracking
          options.release = 'darve-mobile@1.0.0'; // Update with actual version
          
          // Configure beforeSend to filter out expected errors
          options.beforeSend = (event, hint) {
            return _filterEvent(event, hint);
          };
        },
      );
      
      instance._isInitialized = true;
      debugPrint('✅ Error reporting initialized successfully');
    } catch (e) {
      debugPrint('❌ Failed to initialize error reporting: $e');
    }
  }

  /// Filter events before sending to Sentry
  static SentryEvent? _filterEvent(SentryEvent event, Hint hint) {
    // Don't report expected errors (validation errors, user errors)
    final exception = event.throwable;
    if (exception is AppError) {
      if (exception.severity == ErrorSeverity.expected) {
        return null; // Don't send to Sentry
      }
    }

    // Add custom tags and context
    event = event.copyWith(
      tags: {
        ...?event.tags,
        'error_source': 'flutter_app',
      },
    );

    return event;
  }

  /// Report an AppError to Sentry
  Future<void> reportError(AppError error, {StackTrace? stackTrace}) async {
    if (!_isInitialized) {
      debugPrint('⚠️ Error reporting not initialized, skipping report');
      return;
    }

    try {
      // Don't report expected errors
      if (error.severity == ErrorSeverity.expected) {
        debugPrint('ℹ️ Skipping expected error: ${error.message}');
        return;
      }

      await Sentry.captureException(
        error,
        stackTrace: stackTrace,
        withScope: (scope) {
          // Add error-specific context
          scope.setTag('error_type', error.runtimeType.toString());
          scope.setTag('error_severity', error.severity.name);
          
          if (error.code != null) {
            scope.setTag('error_code', error.code!);
          }
          
          if (error.requestId != null) {
            scope.setTag('request_id', error.requestId!);
          }

          // Add error metadata as extra data
          if (error.metadata != null) {
            scope.setExtra('error_metadata', error.metadata!);
          }

          // Add specific context based on error type
          if (error is NetworkError) {
            scope.setTag('http_status_code', error.statusCode?.toString() ?? 'unknown');
            scope.setTag('http_endpoint', error.endpoint ?? 'unknown');
            scope.setTag('http_method', error.method ?? 'unknown');
          } else if (error is ApiError) {
            scope.setExtra('api_field_errors', error.fieldErrors);
            scope.setExtra('has_field_errors', error.hasFieldErrors);
            scope.setExtra('raw_error_message', error.rawErrorMessage);
          }

          // Set user context if available (without PII)
          _setUserContext(scope);
        },
      );

      debugPrint('📤 Error reported to Sentry: ${error.code ?? error.runtimeType}');
    } catch (e) {
      debugPrint('❌ Failed to report error to Sentry: $e');
    }
  }

  /// Report a general exception to Sentry
  Future<void> reportException(
    dynamic exception, {
    StackTrace? stackTrace,
    String? context,
    Map<String, dynamic>? extra,
  }) async {
    if (!_isInitialized) {
      debugPrint('⚠️ Error reporting not initialized, skipping report');
      return;
    }

    try {
      await Sentry.captureException(
        exception,
        stackTrace: stackTrace,
        withScope: (scope) {
          if (context != null) {
            scope.setTag('error_context', context);
          }
          
          if (extra != null) {
            scope.setExtra('extra_data', extra);
          }

          _setUserContext(scope);
        },
      );

      debugPrint('📤 Exception reported to Sentry: ${exception.runtimeType}');
    } catch (e) {
      debugPrint('❌ Failed to report exception to Sentry: $e');
    }
  }

  /// Report a custom message to Sentry
  Future<void> reportMessage(
    String message, {
    SentryLevel level = SentryLevel.error,
    Map<String, dynamic>? extra,
  }) async {
    if (!_isInitialized) {
      debugPrint('⚠️ Error reporting not initialized, skipping report');
      return;
    }

    try {
      await Sentry.captureMessage(
        message,
        level: level,
        withScope: (scope) {
          if (extra != null) {
            scope.setExtra('extra_data', extra);
          }
          _setUserContext(scope);
        },
      );

      debugPrint('📤 Message reported to Sentry: $message');
    } catch (e) {
      debugPrint('❌ Failed to report message to Sentry: $e');
    }
  }

  /// Add breadcrumb for tracking user actions
  void addBreadcrumb({
    required String message,
    String? category,
    SentryLevel level = SentryLevel.info,
    Map<String, dynamic>? data,
  }) {
    if (!_isInitialized) return;

    try {
      Sentry.addBreadcrumb(
        Breadcrumb(
          message: message,
          category: category,
          level: level,
          data: data,
          timestamp: DateTime.now(),
        ),
      );
    } catch (e) {
      debugPrint('❌ Failed to add breadcrumb: $e');
    }
  }

  /// Set user context for error reporting (without PII)
  void _setUserContext(Scope scope) {
    // TODO: Get user info from AuthService without exposing PII
    // Example:
    // final authService = AuthProvider.auth;
    // if (authService.isLoggedIn && authService.user != null) {
    //   scope.setUser(SentryUser(
    //     id: authService.user!.id, // Use hashed ID if needed
    //     username: authService.user!.username,
    //   ));
    // }
  }

  /// Set custom tags for the current session
  void setTag(String key, String value) {
    if (!_isInitialized) return;
    
    try {
      Sentry.configureScope((scope) {
        scope.setTag(key, value);
      });
    } catch (e) {
      debugPrint('❌ Failed to set tag: $e');
    }
  }

  /// Set custom context for the current session
  void setContext(String key, Map<String, dynamic> context) {
    if (!_isInitialized) return;

    try {
      Sentry.configureScope((scope) {
        scope.setExtra(key, context);
      });
    } catch (e) {
      debugPrint('❌ Failed to set context: $e');
    }
  }

  /// Clear user context (e.g., on logout)
  void clearUser() {
    if (!_isInitialized) return;

    try {
      Sentry.configureScope((scope) {
        scope.setUser(null);
      });
    } catch (e) {
      debugPrint('❌ Failed to clear user: $e');
    }
  }

  /// Check if error reporting is initialized
  bool get isInitialized => _isInitialized;
}
