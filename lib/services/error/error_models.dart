// Enhanced error models for comprehensive error handling
import 'package:flutter/foundation.dart';

/// Error severity levels for reporting and handling
enum ErrorSeverity {
  /// Expected errors that users can fix (validation, form errors)
  expected,
  /// Unexpected errors that should be reported but app can continue
  warning,
  /// Unexpected errors that affect functionality
  error,
  /// Critical errors that crash the app or make it unusable
  fatal,
}

/// Base error class for the application
abstract class AppError implements Exception {
  final String message;
  final String? code;
  final ErrorSeverity severity;
  final dynamic originalError;
  final String? requestId;
  final Map<String, dynamic>? metadata;

  const AppError({
    required this.message,
    this.code,
    this.severity = ErrorSeverity.error,
    this.originalError,
    this.requestId,
    this.metadata,
  });

  @override
  String toString() => message;

  /// Convert error to a map for reporting
  Map<String, dynamic> toMap() {
    return {
      'message': message,
      'code': code,
      'severity': severity.name,
      'requestId': requestId,
      'metadata': metadata,
      'originalError': originalError?.toString(),
    };
  }
}

/// API validation errors from server responses
class ValidationError extends AppError {
  final Map<String, List<String>> fieldErrors;

  const ValidationError({
    required super.message,
    required this.fieldErrors,
    super.code,
    super.requestId,
    super.metadata,
  }) : super(severity: ErrorSeverity.expected);

  /// Get user-friendly error message for a specific field
  String? getFieldError(String field) {
    final errors = fieldErrors[field];
    return errors?.isNotEmpty == true ? errors!.first : null;
  }

  /// Get all field errors as a single formatted message
  String get formattedMessage {
    if (fieldErrors.isEmpty) return message;
    
    final errorMessages = <String>[];
    fieldErrors.forEach((field, errors) {
      if (errors.isNotEmpty) {
        errorMessages.add('$field: ${errors.join(', ')}');
      }
    });
    
    return errorMessages.join('\n');
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    map['fieldErrors'] = fieldErrors;
    map['formattedMessage'] = formattedMessage;
    return map;
  }
}

/// Network and HTTP related errors
class NetworkError extends AppError {
  final int? statusCode;
  final String? statusMessage;
  final String? endpoint;
  final String? method;

  const NetworkError({
    required super.message,
    this.statusCode,
    this.statusMessage,
    this.endpoint,
    this.method,
    super.code,
    super.severity = ErrorSeverity.warning,
    super.originalError,
    super.requestId,
    super.metadata,
  });

  factory NetworkError.timeout() => const NetworkError(
        message: 'Connection timeout. Please check your internet connection.',
        code: 'NETWORK_TIMEOUT',
        severity: ErrorSeverity.warning,
      );

  factory NetworkError.noConnection() => const NetworkError(
        message: 'No internet connection.',
        code: 'NO_CONNECTION',
        severity: ErrorSeverity.warning,
      );

  factory NetworkError.serverError({
    int? statusCode,
    String? message,
    String? endpoint,
    String? method,
    String? requestId,
  }) => NetworkError(
        message: message ?? 'Server error. Please try again later.',
        statusCode: statusCode,
        endpoint: endpoint,
        method: method,
        code: 'SERVER_ERROR',
        severity: ErrorSeverity.error,
        requestId: requestId,
      );

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    map['statusCode'] = statusCode;
    map['statusMessage'] = statusMessage;
    map['endpoint'] = endpoint;
    map['method'] = method;
    return map;
  }
}

/// Authentication specific errors
class AuthError extends AppError {
  const AuthError({
    required super.message,
    super.code,
    super.severity = ErrorSeverity.expected,
    super.originalError,
    super.requestId,
    super.metadata,
  });

  // Factory constructors for common auth errors
  factory AuthError.invalidCredentials() => const AuthError(
        message: 'Wrong username or password',
        code: 'INVALID_CREDENTIALS',
      );

  factory AuthError.userNotFound() => const AuthError(
        message: 'Wrong username or password',
        code: 'USER_NOT_FOUND',
      );

  factory AuthError.sessionExpired() => const AuthError(
        message: 'Session Expired! Log in again',
        code: 'SESSION_EXPIRED',
        severity: ErrorSeverity.warning,
      );

  factory AuthError.networkError() => const AuthError(
        message: 'Can not establish connection with Darve backend',
        code: 'NETWORK_ERROR',
        severity: ErrorSeverity.warning,
      );

  factory AuthError.socialLoginFailed(String provider) => AuthError(
        message: 'Failed to sign in with $provider',
        code: 'SOCIAL_LOGIN_FAILED',
      );

  factory AuthError.unknown(dynamic error) => AuthError(
        message: error.toString(),
        code: 'UNKNOWN_ERROR',
        severity: ErrorSeverity.error,
        originalError: error,
      );
}

/// Application logic errors
class AppLogicError extends AppError {
  const AppLogicError({
    required super.message,
    super.code,
    super.severity = ErrorSeverity.error,
    super.originalError,
    super.requestId,
    super.metadata,
  });

  factory AppLogicError.unexpected(dynamic error, {String? context}) => AppLogicError(
        message: context != null 
            ? 'Unexpected error in $context: ${error.toString()}'
            : 'Unexpected error: ${error.toString()}',
        code: 'UNEXPECTED_ERROR',
        severity: ErrorSeverity.fatal,
        originalError: error,
      );
}

/// User-facing errors that should be displayed to users
class UserError extends AppError {
  const UserError({
    required super.message,
    super.code,
    super.severity = ErrorSeverity.expected,
    super.originalError,
    super.requestId,
    super.metadata,
  });

  factory UserError.contentTooShort() => const UserError(
        message: 'Content length too short, need at least 5 characters',
        code: 'CONTENT_TOO_SHORT',
      );

  factory UserError.alreadyFollowing() => const UserError(
        message: 'Already following the user!',
        code: 'ALREADY_FOLLOWING',
      );

  factory UserError.custom(String message, {String? code}) => UserError(
        message: message,
        code: code ?? 'CUSTOM_ERROR',
      );
}
