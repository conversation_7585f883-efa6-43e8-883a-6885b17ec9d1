// Example usage of the new error handling system
// This file demonstrates how to use the enhanced error handling

import 'package:flutter/material.dart';
import 'error_models.dart';
import 'error_reporting_service.dart';
import '../../utils/errors.dart';

/// Example class showing how to use the new error handling system
class ErrorHandlingExample {
  static final ErrorReportingService _errorReporting = ErrorReportingService.instance;
  static final ErrorsHandle _errorDisplay = ErrorsHandle();

  /// Example: Handle API field error response (like validation)
  /// Input: {"error":"username: Letters, numbers and '_'. Minimum 6 characters\npassword: Min 6 characters","req_id":"6185ec98-d7d9-43d8-b432-d30e7c9bd9de"}
  static void handleApiFieldErrorExample() {
    // This would typically be parsed by Error<PERSON>arser from a DioException
    final apiError = ApiError.withFields(
      message: 'API request failed',
      fieldErrors: {
        'username': ['Letters, numbers and \'_\'. Minimum 6 characters'],
        'password': ['Min 6 characters'],
      },
      code: 'API_FIELD_ERROR',
      requestId: '6185ec98-d7d9-43d8-b432-d30e7c9bd9de',
    );

    // Display to user (will show formatted field errors)
    _errorDisplay.displayErrorToast(apiError);

    // This won't be reported to Sentry because it's an expected error
    debugPrint('API field error handled: ${apiError.displayMessage}');
  }

  /// Example: Handle simple API error response
  static void handleSimpleApiErrorExample() {
    final apiError = ApiError.simple(
      message: 'Invalid request format',
      code: 'INVALID_REQUEST',
      requestId: 'req_456',
    );

    // Display to user
    _errorDisplay.displayErrorToast(apiError);

    debugPrint('Simple API error handled: ${apiError.displayMessage}');
  }

  /// Example: Handle network error
  static void handleNetworkErrorExample() {
    final networkError = NetworkError.serverError(
      statusCode: 500,
      message: 'Internal server error',
      endpoint: '/api/login',
      method: 'POST',
      requestId: 'abc123',
    );

    // Display to user
    _errorDisplay.displayErrorToast(networkError);

    // This will be reported to Sentry because it's an unexpected error
    _errorReporting.reportError(networkError);
  }

  /// Example: Handle authentication error
  static void handleAuthErrorExample() {
    final authError = AuthError.sessionExpired();

    // Display to user
    _errorDisplay.displayErrorToast(authError);

    // Add breadcrumb for tracking
    _errorReporting.addBreadcrumb(
      message: 'User session expired',
      category: 'auth',
      data: {'action': 'auto_logout'},
    );
  }

  /// Example: Handle unexpected application error
  static void handleUnexpectedErrorExample() {
    try {
      // Some operation that might fail
      throw Exception('Something went wrong in the app');
    } catch (e, stackTrace) {
      final appError = AppLogicError.unexpected(e, context: 'user_profile_load');

      // Display to user
      _errorDisplay.displayErrorToast(appError);

      // Report to Sentry with stack trace
      _errorReporting.reportError(appError, stackTrace: stackTrace);
    }
  }

  /// Example: Add breadcrumbs for user actions
  static void trackUserAction(String action, Map<String, dynamic>? data) {
    _errorReporting.addBreadcrumb(
      message: 'User action: $action',
      category: 'user_action',
      data: data,
    );
  }

  /// Example: Set user context for error reporting
  static void setUserContext(String userId, String username) {
    _errorReporting.setTag('user_id', userId);
    _errorReporting.setContext('user', {
      'username': username,
      'logged_in': true,
    });
  }

  /// Example: Clear user context on logout
  static void clearUserContext() {
    _errorReporting.clearUser();
  }

  /// Example: Report custom message
  static void reportCustomMessage(String message, {Map<String, dynamic>? extra}) {
    _errorReporting.reportMessage(
      message,
      extra: extra,
    );
  }

  /// Example: Display success message
  static void showSuccess(String message) {
    _errorDisplay.displaySuccessToast(message);
  }

  /// Example: Display info message
  static void showInfo(String message) {
    _errorDisplay.displayInfoToast(message);
  }
}

/// Widget example showing error handling in UI
class ErrorHandlingExampleWidget extends StatelessWidget {
  const ErrorHandlingExampleWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Error Handling Examples')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Error Handling Examples',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            
            ElevatedButton(
              onPressed: () {
                ErrorHandlingExample.handleApiFieldErrorExample();
              },
              child: const Text('Show API Field Error'),
            ),

            const SizedBox(height: 10),

            ElevatedButton(
              onPressed: () {
                ErrorHandlingExample.handleSimpleApiErrorExample();
              },
              child: const Text('Show Simple API Error'),
            ),
            
            const SizedBox(height: 10),
            
            ElevatedButton(
              onPressed: () {
                ErrorHandlingExample.handleNetworkErrorExample();
              },
              child: const Text('Show Network Error'),
            ),
            
            const SizedBox(height: 10),
            
            ElevatedButton(
              onPressed: () {
                ErrorHandlingExample.handleAuthErrorExample();
              },
              child: const Text('Show Auth Error'),
            ),
            
            const SizedBox(height: 10),
            
            ElevatedButton(
              onPressed: () {
                ErrorHandlingExample.handleUnexpectedErrorExample();
              },
              child: const Text('Show Unexpected Error'),
            ),
            
            const SizedBox(height: 20),
            
            ElevatedButton(
              onPressed: () {
                ErrorHandlingExample.showSuccess('Operation completed successfully!');
              },
              style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
              child: const Text('Show Success Message'),
            ),
            
            const SizedBox(height: 10),
            
            ElevatedButton(
              onPressed: () {
                ErrorHandlingExample.showInfo('This is an informational message');
              },
              style: ElevatedButton.styleFrom(backgroundColor: Colors.blue),
              child: const Text('Show Info Message'),
            ),
            
            const SizedBox(height: 20),
            
            const Text(
              'Instructions:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            const Text(
              '1. API field errors show field-specific messages\n'
              '2. Simple API errors show single error messages\n'
              '3. Network errors are reported to Sentry\n'
              '4. Auth errors trigger appropriate actions\n'
              '5. Unexpected errors are logged and reported\n'
              '6. Success and info messages provide feedback',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }
}
