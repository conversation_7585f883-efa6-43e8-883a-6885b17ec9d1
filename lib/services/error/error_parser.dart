// Error parser for handling API error responses
import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'error_models.dart';

/// Service for parsing various error response formats from the API
class ErrorParser {
  /// Parse error response from DioException
  static AppError parseFromDioException(DioException error) {
    final response = error.response;
    final requestId = _extractRequestId(response);
    final endpoint = error.requestOptions.uri.toString();
    final method = error.requestOptions.method.toUpperCase();

    // Handle different types of Dio errors
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return NetworkError.timeout();

      case DioExceptionType.connectionError:
        return NetworkError.noConnection();

      case DioExceptionType.cancel:
        return NetworkError(
          message: 'Request was cancelled',
          code: 'REQUEST_CANCELLED',
          severity: ErrorSeverity.warning,
          endpoint: endpoint,
          method: method,
        );

      case DioExceptionType.badResponse:
        return _parseBadResponse(response, endpoint, method, requestId);

      default:
        return NetworkError(
          message: 'Network error: ${error.message}',
          code: 'NETWORK_ERROR',
          severity: ErrorSeverity.error,
          endpoint: endpoint,
          method: method,
          originalError: error,
        );
    }
  }

  /// Parse bad response (4xx, 5xx status codes)
  static AppError _parseBadResponse(
    Response? response,
    String endpoint,
    String method,
    String? requestId,
  ) {
    if (response == null) {
      return NetworkError.serverError(
        endpoint: endpoint,
        method: method,
        requestId: requestId,
      );
    }

    final statusCode = response.statusCode ?? 0;
    final responseData = response.data;

    // Try to parse structured error response
    if (responseData != null) {
      try {
        final parsedError = _parseStructuredError(
          responseData,
          statusCode,
          endpoint,
          method,
          requestId,
        );
        if (parsedError != null) return parsedError;
      } catch (e) {
        debugPrint('Failed to parse structured error: $e');
      }
    }

    // Fallback to status code based errors
    return _createStatusCodeError(statusCode, endpoint, method, requestId, responseData);
  }

  /// Parse structured error responses like {"error":"username: Letters, numbers and '_'. Minimum 6 characters\npassword: Min 6 characters","req_id":"6185ec98-d7d9-43d8-b432-d30e7c9bd9de"}
  static AppError? _parseStructuredError(
    dynamic responseData,
    int statusCode,
    String endpoint,
    String method,
    String? requestId,
  ) {
    Map<String, dynamic> errorData;

    // Handle different response data types
    if (responseData is String) {
      try {
        errorData = jsonDecode(responseData);
      } catch (e) {
        return null; // Not a JSON string
      }
    } else if (responseData is Map<String, dynamic>) {
      errorData = responseData;
    } else {
      return null; // Unsupported format
    }

    // Extract request ID if present
    final responseRequestId = errorData['req_id'] as String? ?? requestId;

    // Check for error field
    if (errorData.containsKey('error')) {
      final errorMessage = errorData['error'] as String;

      // Check for auth-specific errors first
      if (statusCode == 401 || errorMessage.toLowerCase().contains('unauthorized') ||
          errorMessage.toLowerCase().contains('invalid credentials')) {
        return AuthError(
          message: 'Unauthorized. Please log in again.',
          code: 'UNAUTHORIZED',
          severity: ErrorSeverity.warning,
          requestId: responseRequestId,
        );
      }

      // Parse as API error (handles both field-specific and simple errors)
      return _parseApiError(
        errorMessage,
        statusCode,
        endpoint,
        method,
        responseRequestId,
        errorData,
      );
    }

    // Check for other common error formats
    if (errorData.containsKey('message')) {
      final message = errorData['message'] as String;
      return _createErrorFromMessage(
        message,
        statusCode,
        endpoint,
        method,
        responseRequestId,
        errorData,
      );
    }

    return null; // Couldn't parse as structured error
  }

  /// Parse API errors with field-specific messages or simple messages
  static ApiError _parseApiError(
    String errorMessage,
    int statusCode,
    String endpoint,
    String method,
    String? requestId,
    Map<String, dynamic> errorData,
  ) {
    // Simple API error
    return ApiError.simple(
      message: errorMessage,
      code: 'API_ERROR',
      requestId: requestId,
      metadata: {
        'statusCode': statusCode,
        'endpoint': endpoint,
        'method': method,
        'originalResponse': errorData,
      },
      severity: _determineSeverityFromStatusCode(statusCode),
    );
  }

  /// Determine error severity based on HTTP status code
  static ErrorSeverity _determineSeverityFromStatusCode(int statusCode) {
    if (statusCode >= 400 && statusCode < 500) {
      return ErrorSeverity.expected; // Client errors are usually expected
    } else if (statusCode >= 500) {
      return ErrorSeverity.error; // Server errors are unexpected
    }
    return ErrorSeverity.warning;
  }

  /// Create appropriate error from message and status code
  static AppError _createErrorFromMessage(
    String message,
    int statusCode,
    String endpoint,
    String method,
    String? requestId,
    Map<String, dynamic>? errorData,
  ) {
    // Determine error type based on status code and message
    if (statusCode == 401 || message.toLowerCase().contains('unauthorized')) {
      return AuthError(
        message: 'Unauthorized. Please log in again.',
        code: 'UNAUTHORIZED',
        severity: ErrorSeverity.warning,
        requestId: requestId,
      );
    }

    if (statusCode == 403) {
      return NetworkError(
        message: 'Access forbidden',
        statusCode: statusCode,
        code: 'FORBIDDEN',
        endpoint: endpoint,
        method: method,
        requestId: requestId,
      );
    }

    if (statusCode == 404) {
      return NetworkError(
        message: 'Resource not found',
        statusCode: statusCode,
        code: 'NOT_FOUND',
        endpoint: endpoint,
        method: method,
        requestId: requestId,
      );
    }

    if (statusCode >= 500) {
      return NetworkError.serverError(
        statusCode: statusCode,
        message: message,
        endpoint: endpoint,
        method: method,
        requestId: requestId,
      );
    }

    // Default to user error for 4xx codes
    return UserError(
      message: message,
      code: 'API_ERROR',
      requestId: requestId,
      metadata: {
        'statusCode': statusCode,
        'endpoint': endpoint,
        'method': method,
        'originalResponse': errorData,
      },
    );
  }

  /// Create error based on status code when no structured response is available
  static AppError _createStatusCodeError(
    int statusCode,
    String endpoint,
    String method,
    String? requestId,
    dynamic responseData,
  ) {
    switch (statusCode) {
      case 401:
        return AuthError(
          message: 'Unauthorized. Please log in again.',
          code: 'UNAUTHORIZED',
          severity: ErrorSeverity.warning,
          requestId: requestId,
        );
      case 403:
        return NetworkError(
          message: 'Access forbidden',
          statusCode: statusCode,
          code: 'FORBIDDEN',
          endpoint: endpoint,
          method: method,
          requestId: requestId,
        );
      case 404:
        return NetworkError(
          message: 'Resource not found',
          statusCode: statusCode,
          code: 'NOT_FOUND',
          endpoint: endpoint,
          method: method,
          requestId: requestId,
        );
      case 429:
        return NetworkError(
          message: 'Too many requests. Please try again later.',
          statusCode: statusCode,
          code: 'RATE_LIMITED',
          severity: ErrorSeverity.warning,
          endpoint: endpoint,
          method: method,
          requestId: requestId,
        );
      default:
        if (statusCode >= 500) {
          return NetworkError.serverError(
            statusCode: statusCode,
            endpoint: endpoint,
            method: method,
            requestId: requestId,
          );
        } else {
          return NetworkError(
            message: 'Request failed with status $statusCode',
            statusCode: statusCode,
            code: 'HTTP_ERROR',
            endpoint: endpoint,
            method: method,
            requestId: requestId,
            metadata: {'responseData': responseData?.toString()},
          );
        }
    }
  }

  /// Extract request ID from response headers or data
  static String? _extractRequestId(Response? response) {
    if (response == null) return null;

    // Try to get from headers first
    final headerRequestId = response.headers.value('x-request-id') ??
        response.headers.value('request-id') ??
        response.headers.value('req-id');

    if (headerRequestId != null) return headerRequestId;

    // Try to get from response data
    if (response.data is Map<String, dynamic>) {
      final data = response.data as Map<String, dynamic>;
      return data['req_id'] as String? ?? data['request_id'] as String?;
    }

    return null;
  }
}
