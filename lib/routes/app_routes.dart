class AppRoutes {
  // Auth routes
  static const String login = '/login';
  static const String signup = '/signup';
  static const String forgotPassword = '/forgot-password';

  // Main routes
  static const String home = '/home';
  static const String chat = '/chat';

  // Settings routes
  static const String settings = '/settings';
  static const String privacySettings = '/settings/privacy';
  static const String contactSupport = '/settings/contact';
  static const String moreInfo = '/settings/more-info';

  // Feature routes
  static const String search = '/search';
  static const String notifications = '/notifications';
  static const String profile = '/profile';

  // Recording routes
  static const String camera = '/camera';
  static const String myChallenges = '/my-challenges';

  // Testing routes
  static const String errorTest = '/error-test';

  // Initial route based on auth state
  static const String initial = home;
}
