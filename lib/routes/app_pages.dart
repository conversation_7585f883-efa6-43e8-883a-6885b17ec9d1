import 'package:get/get.dart';
import 'package:darve/pages/SignIn.dart';
import 'package:darve/pages/SignUp.dart';
import 'package:darve/pages/forgot_password_page.dart';
import 'package:darve/pages/ScreenHandler.dart';
import 'package:darve/pages/Settings.dart';
import 'package:darve/pages/SettingsPages/PrivacySettings.dart';
import 'package:darve/pages/SettingsPages/ContactUsSupport.dart';
import 'package:darve/pages/SettingsPages/MoreInfo.dart';
import 'package:darve/pages/SearchPage.dart';
import 'package:darve/pages/NotificationsPage.dart';
import 'package:darve/pages/profile_page.dart';
import 'package:darve/pages/RecordingsPages/MyChallenges.dart';
import 'package:darve/components/RecordingsPage/CameraScreen.dart';
import 'package:darve/pages/ChatPages/chat_screen.dart';
import 'package:darve/controllers/auth_controller.dart';
import 'package:darve/controllers/register_controller.dart';
import 'package:darve/controllers/forgot_password_controller.dart';
import 'package:darve/controllers/chat_list_controller.dart';
import 'package:darve/routes/app_routes.dart';
import 'package:darve/routes/auth_middleware.dart';
import 'package:darve/services/error/error_example.dart';

class AppPages {
  static final List<GetPage> pages = [
    // Login page - no middleware needed
    GetPage(
      name: AppRoutes.login,
      page: () => SignInPage(),
      binding: BindingsBuilder(() {
        Get.lazyPut<AuthController>(() => AuthController());
      }),
    ),
    
    // Home page - requires authentication
    GetPage(
      name: AppRoutes.home,
      page: () => const ScreenHandler(),
      middlewares: [AuthMiddleware()],
      binding: BindingsBuilder(() {
        Get.lazyPut<AuthController>(() => AuthController());
        Get.lazyPut<ChatListController>(() => ChatListController());
      }),
    ),
    
    // Auth routes
    GetPage(
      name: AppRoutes.signup,
      page: () => SignUpPage(),
      binding: BindingsBuilder(() {
        Get.lazyPut<RegisterController>(() => RegisterController());
      }),
    ),

    GetPage(
      name: AppRoutes.forgotPassword,
      page: () => const ForgotPasswordPage(),
      binding: BindingsBuilder(() {
        Get.lazyPut<ForgotPasswordController>(() => ForgotPasswordController());
      }),
    ),

    // Settings routes - requires authentication
    GetPage(
      name: AppRoutes.settings,
      page: () => const Settings(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: AppRoutes.privacySettings,
      page: () => const PrivacySettings(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: AppRoutes.contactSupport,
      page: () => const ContactUsSupport(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: AppRoutes.moreInfo,
      page: () => const MoreInfo(),
      middlewares: [AuthMiddleware()],
    ),

    // Feature routes - requires authentication
    GetPage(
      name: AppRoutes.search,
      page: () => const SearchPage(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: AppRoutes.notifications,
      page: () => const NotificationsPage(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: AppRoutes.profile,
      page: () {
        final args = Get.arguments as Map<String, String>;
        return ProfilePage(
          args['userId']!,
          args['username']!,
          args['imageUrl']!,
          args['fullName']!,
        );
      },
      middlewares: [AuthMiddleware()],
    ),

    // Chat screen - requires authentication
    GetPage(
      name: AppRoutes.chat,
      page: () {
        final args = Get.arguments as Map<String, String?>;
        return ChatScreen(
          chatId: args['chatId']!,
          title: args['title']!,
          avatarUrl: args['avatarUrl']!,
          userId: args['userId']!,
        );
      },
      middlewares: [AuthMiddleware()],
    ),

    // Recording routes - requires authentication
    GetPage(
      name: AppRoutes.myChallenges,
      page: () => const MyChallenges(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: AppRoutes.camera,
      page: () {
        final args = Get.arguments as Map<String, dynamic>;
        return CameraScreen(
          cameras: args['cameras'],
          taskId: args['taskId'],
          cb: args['callback'],
        );
      },
      middlewares: [AuthMiddleware()],
    ),

    // Error testing route - no auth required for testing
    GetPage(
      name: AppRoutes.errorTest,
      page: () => const ErrorHandlingExampleWidget(),
    ),
  ];
}
